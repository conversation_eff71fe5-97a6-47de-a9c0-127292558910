# User Switching Test Guide - API Headers Focus

## Testing Steps

### 1. Initial Setup

- Login with your main user account
- Open browser developer tools (F12)
- Go to Console tab to see debug logs
- Go to Network tab to monitor API calls

### 2. Check Initial State

```javascript
// Run in browser console to check current state
console.log("Initial userData:", localStorage.getItem("userData"));
console.log("Initial switchUserData:", localStorage.getItem("SwitchUserData"));
console.log("Initial UserId:", localStorage.getItem("UserId"));
console.log("Initial BrandId:", localStorage.getItem("BrandId"));
```

### 3. Monitor API Headers

- In Network tab, filter for XHR/Fetch requests
- Look for these specific API calls:
  - `/user-admin-profile/`
  - `/user-admim-dashboard/?from_date=...&to_date=...`
- Check Request Headers for `user` and `brand` values

### 4. Test User Switching

1. **Navigate to Dashboard** - Note the current user's name in welcome card
2. **Clear Network Tab** - Clear previous requests
3. **Open Sidebar User Dropdown** - Click on user profile in sidebar
4. **Select Different User** - Choose another user from dropdown
5. **Watch Console Logs** - You should see:

   - "Switching to user: [user object]"
   - "User switched successfully. New active user: [username]"
   - "localStorage UserId: [new user id]"
   - "localStorage BrandId: [new brand id]"
   - "Dashboard: Active user changed, refetching data for: [username]"
   - "Fetching USER_ADMIN_PROFILE with userId: [new id] brandId: [new brand id]"
   - "Fetching user-admim-dashboard with userId: [new id] brandId: [new brand id]"
   - "API Request Headers Debug: [showing new user/brand IDs for each API call]"

6. **Check Network Tab** - Look for new API requests:
   - `/user-admin-profile/` should have headers: `user: [switched user id]`, `brand: [switched brand id]`
   - `/user-admim-dashboard/` should have headers: `user: [switched user id]`, `brand: [switched brand id]`

### 4. Verify Dashboard Updates

- **Welcome Card** should show switched user's name and profile image
- **Dashboard Data** should refresh and show switched user's analytics
- **All API Calls** should use switched user's headers (check Network tab)

### 5. Test Switch Back

1. **Select Original User** from dropdown
2. **Verify Console Logs** show switching back to original user
3. **Check Dashboard** shows original user's data again

### 6. Check localStorage Values

```javascript
// Run after switching users
console.log("After switch userData:", localStorage.getItem("userData"));
console.log(
  "After switch switchUserData:",
  localStorage.getItem("SwitchUserData")
);
console.log("After switch UserId:", localStorage.getItem("UserId"));
console.log("After switch BrandId:", localStorage.getItem("BrandId"));
```

## Expected Behavior

### ✅ What Should Work:

- User switching updates localStorage UserId and BrandId immediately
- Dashboard welcome card shows switched user's name
- All API calls use switched user's ID and brand ID in headers
- Dashboard data refreshes automatically when user switches
- Console shows debug logs confirming the switch
- No page reload required

### ❌ Common Issues to Check:

- If dashboard doesn't update: Check console for API errors
- If wrong data shows: Verify API headers in Network tab
- If switching fails: Check console for error messages
- If localStorage not updating: Check UserContext implementation

## Debug Commands

```javascript
// Check current active user
const userData = JSON.parse(localStorage.getItem("userData"));
const switchUserData = JSON.parse(localStorage.getItem("SwitchUserData"));
const activeUser = switchUserData || userData;
console.log("Current active user:", activeUser?.name);

// Check API headers
// Go to Network tab and look for API calls to see if correct user/brand headers are being sent
```

## Troubleshooting

If user switching is not working:

1. **Check Console Errors** - Look for any JavaScript errors
2. **Verify API Headers** - In Network tab, check if API calls have correct user/brand headers
3. **Check localStorage** - Ensure UserId and BrandId are updating
4. **Verify UserContext** - Make sure UserProvider is wrapping the app
5. **Check Axios Interceptor** - Ensure it's reading from correct localStorage keys

## 🔍 **DEBUGGING - Check These Console Logs:**

When you switch from Tahid to Samarth, you should see:

```javascript
// 1. User object structure
"User object structure: {
  id: [Samarth's ID],
  user_id: [Samarth's user_id],
  brand_id: [Samarth's brand_id],
  name: 'Samarth'
}"

// 2. UserContext logs
"UserContext switchUser called with: [Samarth user object]"
"Setting localStorage - UserId: [Samarth's ID] BrandId: [Samarth's brand_id]"

// 3. API Headers
"API Request Headers Debug: {
  url: '/user-admin-profile/',
  activeUser: 'Samarth',  // <- Should be Samarth now
  userId: [Samarth's ID],  // <- Should be Samarth's ID
  brandId: [Samarth's brand_id],  // <- Should be Samarth's brand_id
}"

// 4. API Response should be Samarth's data
"USER_ADMIN_PROFILE response: {
  data: { name: 'Samarth', ... }  // <- Should be Samarth's profile
}"
```

## ❌ **If Still Showing Tahid's Data:**

**Problem**: User object mein proper user_id ya brand_id nahi aa rahe

**Check**: Console mein "User object structure" log dekho - kya values undefined hain?

## Success Criteria

✅ User can switch between accounts using sidebar dropdown
✅ Dashboard immediately shows switched user's data
✅ All API calls use switched user's context
✅ localStorage values update automatically
✅ No page reload required
✅ Can switch back to original user seamlessly
