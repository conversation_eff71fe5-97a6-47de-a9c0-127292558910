# User Switching Test Guide

## Testing Steps

### 1. Initial Setup
- Login with your main user account
- Open browser developer tools (F12)
- Go to Console tab to see debug logs

### 2. Check Initial State
```javascript
// Run in browser console to check current state
console.log("Initial userData:", localStorage.getItem("userData"));
console.log("Initial switchUserData:", localStorage.getItem("SwitchUserData"));
console.log("Initial UserId:", localStorage.getItem("UserId"));
console.log("Initial BrandId:", localStorage.getItem("BrandId"));
```

### 3. Test User Switching
1. **Navigate to Dashboard** - Note the current user's name in welcome card
2. **Open Sidebar User Dropdown** - Click on user profile in sidebar
3. **Select Different User** - Choose another user from dropdown
4. **Watch Console Logs** - You should see:
   - "Switching to user: [user object]"
   - "User switched successfully. New active user: [username]"
   - "localStorage UserId: [new user id]"
   - "localStorage BrandId: [new brand id]"
   - "Dashboard: Active user changed, refetching data for: [username]"
   - "API Request Headers Debug: [showing new user/brand IDs]"

### 4. Verify Dashboard Updates
- **Welcome Card** should show switched user's name and profile image
- **Dashboard Data** should refresh and show switched user's analytics
- **All API Calls** should use switched user's headers (check Network tab)

### 5. Test Switch Back
1. **Select Original User** from dropdown
2. **Verify Console Logs** show switching back to original user
3. **Check Dashboard** shows original user's data again

### 6. Check localStorage Values
```javascript
// Run after switching users
console.log("After switch userData:", localStorage.getItem("userData"));
console.log("After switch switchUserData:", localStorage.getItem("SwitchUserData"));
console.log("After switch UserId:", localStorage.getItem("UserId"));
console.log("After switch BrandId:", localStorage.getItem("BrandId"));
```

## Expected Behavior

### ✅ What Should Work:
- User switching updates localStorage UserId and BrandId immediately
- Dashboard welcome card shows switched user's name
- All API calls use switched user's ID and brand ID in headers
- Dashboard data refreshes automatically when user switches
- Console shows debug logs confirming the switch
- No page reload required

### ❌ Common Issues to Check:
- If dashboard doesn't update: Check console for API errors
- If wrong data shows: Verify API headers in Network tab
- If switching fails: Check console for error messages
- If localStorage not updating: Check UserContext implementation

## Debug Commands

```javascript
// Check current active user
const userData = JSON.parse(localStorage.getItem("userData"));
const switchUserData = JSON.parse(localStorage.getItem("SwitchUserData"));
const activeUser = switchUserData || userData;
console.log("Current active user:", activeUser?.name);

// Check API headers
// Go to Network tab and look for API calls to see if correct user/brand headers are being sent
```

## Troubleshooting

If user switching is not working:

1. **Check Console Errors** - Look for any JavaScript errors
2. **Verify API Headers** - In Network tab, check if API calls have correct user/brand headers
3. **Check localStorage** - Ensure UserId and BrandId are updating
4. **Verify UserContext** - Make sure UserProvider is wrapping the app
5. **Check Axios Interceptor** - Ensure it's reading from correct localStorage keys

## Success Criteria

✅ User can switch between accounts using sidebar dropdown
✅ Dashboard immediately shows switched user's data
✅ All API calls use switched user's context
✅ localStorage values update automatically
✅ No page reload required
✅ Can switch back to original user seamlessly
