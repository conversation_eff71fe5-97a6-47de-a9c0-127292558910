import React, { Suspense, useContext, useEffect, useState } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import {
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  MenuItem,
  Box,
  CssBaseline,
  // Tooltip,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  Popover,
  CircularProgress,
  Dialog,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import MenuIcon from "@mui/icons-material/Menu";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import "../layouts.css";
import {
  clearStorage,
  fetchFromStorage,
  saveToStorage,
} from "../../helpers/context/storage";
import siteConstant from "../../helpers/constant/siteConstant";
import { IntlContext } from "../../App";
import styled from "@emotion/styled";
import Scrolltop from "../ScrollTop/Scrolltop";
import DeleteDialogueModel from "../../views/admin/common/deleteDialogue";
import Android12Switch from "../../views/components/UploadPost/Android12Switch.jsx";
import { useSelector } from "react-redux";
import apiInstance from "../../helpers/Axios/axiosINstance";
import { URL } from "../../helpers/constant/Url";
import dropDownNav from "../../assets/images/svg_icon/DropDown_Nav.svg";
import Notification from "../../assets/images/svg_icon/notification.svg";
import { useBrand } from "../../helpers/context/BrandContext";
import { useUser } from "../../helpers/context/UserContext";
import { SocketContext } from "../../helpers/context/socket";
import Notifypage from "../../views/components/notification/index.jsx";
const UploadPost = React.lazy(() =>
  import("../../views/components/UploadPost")
);
const Feedpage = React.lazy(() => import("../../views/components/feedback"));

const theme = createTheme({
  transitions: {
    create: (props, options) => {
      if (!Array.isArray(props)) {
        return "";
      }
    },
    easing: {
      sharp: "cubic-bezier(0.4, 0, 0.6, 1)",
    },
    duration: {
      enteringScreen: 225,
      leavingScreen: 195,
    },
    delay: 150,
  },
  spacing: (factor) => `${0.25 * factor}rem`,
  palette: {
    primary: {
      main: "#674941", // Brown color for active items
    },
    text: {
      primary: "#674941", // Brown color for active text
      secondary: "#9E9E9E", // Gray color for inactive text
    },
  },
});

const NavItem = styled(Box)(({ theme, active }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  cursor: "pointer",
  color: active ? "#674941" : "#9E9E9E", // Brown when active, gray when inactive
  "& .nav-icon": {
    color: active ? "#674941" : "#9E9E9E",
    transition: "color 0.3s ease",
  },
  "& .nav-text": {
    fontSize: "12px",
    marginTop: "4px",
    fontWeight: active ? "600" : "400",
  },
  "& .nav-dot": {
    height: "4px",
    width: "4px",
    borderRadius: "50%",
    backgroundColor: "#674941",
    marginTop: "4px",
    visibility: active ? "visible" : "hidden",
  },
}));

const Navbar = () => {
  const rProfile = useSelector((state) => state?.profile?.data);
  const [currentTab, setCurrentTab] = useState(
    fetchFromStorage(siteConstant?.INDENTIFIERS.currentTab)
  );
  const [anchorEl, setAnchorEl] = useState(null);
  const [dropdownAnchorEl, setDropdownAnchorEl] = useState(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const [navbarProps, setNavbarProps] = useState([]);

  // Use UserContext for user management
  const {
    userData,
    switchUserData,
    activeUser,
    switchUser: switchUserInContext,
    getCurrentBrandId,
  } = useUser();

  const brandId = getCurrentBrandId();
  const currentUser = activeUser;

  // Debug logging (can be removed in production)
  // console.log("Debug - userData:", userData);
  // console.log("Debug - switchUserData:", switchUserData);
  // console.log("Debug - currentUser:", currentUser);

  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const firstName = currentUser?.name?.split(" ")[0];
  const [isScrolled, setIsScrolled] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [isChecked, setIsChecked] = useState(
    currentTab === localesData?.ADMIN_TAB
  );
  const [isFeedOpen, setIsFeedOpen] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  const { selectedBrand, loadingBrand, setBrands, brands } = useBrand();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState(switchUserData);

  // User switching functionality
  const [availableUsers, setAvailableUsers] = useState([]);
  console.log("availableUsers", availableUsers);

  const [loadingUsers, setLoadingUsers] = useState(false);

  // Socket context for Flowkar messaging
  const socket = useContext(SocketContext);
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;

  // Media queries for responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "lg"));

  // Brand Management
  const { handleBrandSelect } = useBrand();
  const { resetBrand } = useBrand();
  const [clickedBrandId, setClickedBrandId] = useState(null);

  const onBrandChange = (brand) => {
    handleBrandSelect(brand);
    localStorage.setItem("BrandId", brand?.id || "");
    handleDropdownClose();
  };

  // User switching function
  const switchUser = async (user) => {
    console.log("Switching to user:", user);
    console.log("Current userData before switch:", userData);
    console.log("Current switchUserData before switch:", switchUserData);
    try {
      setLoadingUsers(true);

      // Check if switching back to original user
      const isOriginalUser =
        userData &&
        (user.id === userData.user_id || user.user_id === userData.user_id);

      if (isOriginalUser) {
        console.log("Switching back to original user");
        // Clear switch user data when going back to original user
        setSelectedUser(null);
        switchUserInContext(null); // Use UserContext
      } else {
        // Update UI immediately for better user experience
        console.log("Setting selectedUser and switchUserData to:", user);
        setSelectedUser(user);
        switchUserInContext(user); // Use UserContext
      }

      console.log("Saved to storage:", isOriginalUser ? null : user);

      // Call the switch user API to get updated user data
      const response = await apiInstance.get(URL.All_USERS, {
        headers: {
          brand: brandId,
          user: user.user_id || user.id,
        },
      });

      if (response?.data?.status) {
        console.log("User switch completed successfully");
        // The UserContext will handle updating localStorage automatically
      }
    } catch (error) {
      console.error("Failed to switch user:", error);
      // Revert UI changes on error - UserContext will handle storage revert
      setSelectedUser(switchUserData);
    } finally {
      setLoadingUsers(false);
    }
  };

  const onUserChange = async (user) => {
    console.log("User change requested:", user);
    // Use the switchUser function
    await switchUser(user);
    handleDropdownClose();
  };

  const fetchAvailableUsers = async () => {
    try {
      // Use GET_INVITEE_USERS to get list of users that can be switched to
      const response = await apiInstance.get(URL.GET_INVITEE_USERS, {
        headers: {
          brand: brandId,
          user: currentUser?.user_id,
        },
      });
      const users = response?.data?.data ?? [];
      const usersList = Array.isArray(users) ? users : [];

      // Add the original user to the list if not already present
      if (userData && userData.user_id) {
        const originalUserExists = usersList.some(
          (user) =>
            user.id === userData.user_id || user.user_id === userData.user_id
        );

        if (!originalUserExists) {
          const originalUser = {
            id: userData.user_id,
            user_id: userData.user_id,
            name: userData.name,
            email: userData.email,
            profile_image: userData.profile_image,
            // Add any other necessary fields
          };
          usersList.unshift(originalUser); // Add to beginning of list
        }
      }

      setAvailableUsers(usersList);
    } catch (error) {
      console.error("Failed to fetch available users:", error);
      setAvailableUsers([]);
    }
  };

  useEffect(() => {
    fetchAvailableUsers();
  }, [brandId, currentUser?.id]);

  // Effect to handle switchUserData changes
  useEffect(() => {
    console.log("switchUserData changed:", switchUserData);
    console.log("currentUser updated:", currentUser);
  }, [switchUserData, currentUser]);

  const GetBrands = async () => {
    try {
      setLoading(true);
      const response = await apiInstance.get(URL.GET_BRANDS);
      const fetchedBrands = response.data.data || [];
      setBrands(fetchedBrands);
    } catch (error) {
      console.error("Error fetching brands:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    GetBrands();
  }, []);

  const handleBrandClick = async () => {
    try {
      const response = await apiInstance.get(URL.CURRENT_BRAND, {
        headers: {
          brand: clickedBrandId,
        },
      });
    } catch (error) {
      console.error("Error fetching brands:", error);
    }
  };

  useEffect(() => {
    handleBrandClick();
  }, [clickedBrandId]);

  const handleScroll = () => {
    const scrollDiv = document.getElementById("scrollDiv");
    setIsScrolled(scrollDiv?.scrollTop > 0);
  };

  const [isVisible, setIsVisible] = useState(false);

  const toggleVisibility = () => {
    const scrollDiv = document.getElementById("scrollDiv");
    if (scrollDiv?.scrollTop > 200) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  useEffect(() => {
    const scrollDiv = document.getElementById("scrollDiv");
    scrollDiv?.addEventListener("scroll", handleScroll);
    scrollDiv?.addEventListener("scroll", toggleVisibility);
    return () => {
      scrollDiv?.removeEventListener("scroll", handleScroll);
      scrollDiv?.removeEventListener("scroll", toggleVisibility);
    };
  }, []);

  useEffect(() => {
    if (location?.pathname?.includes("/admin")) {
      setCurrentTab(localesData?.ADMIN_TAB);
    } else {
      setCurrentTab(localesData?.USER_TAB);
    }
  }, []);

  useEffect(() => {
    const adminNavItems = [
      {
        id: 1,
        name: "User Management",
        link: "/admin/user-management",
        logo: (
          <img
            alt="User Management"
            className="h-8 w-8 p-1.5 bg-white rounded-[14px]"
            src={siteConstant.ICONS.User_Icon}
          />
        ),
      },
    ];

    const userNavItems = [
      {
        id: 1,
        name: `Profile`,
        link: "/profile",
        logo: (isActive) => (
          <img
            alt="User Photo"
            className={`h-8 w-8 rounded-[14px] border-[2px] p-1 ${
              isActive ? "border-white border-[2px]" : "border-Red"
            }`}
            src={
              currentUser?.profile_image ||
              siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
            }
          />
        ),
      },
      {
        id: 2,
        name: "Home",
        link: "/dashboard",
        logo: (
          <img
            alt="Dashboard"
            className="h-8 w-8 p-1.5"
            src={
              location.pathname === "/dashboard"
                ? siteConstant.ICONS.Dashboard_Icon_Active
                : siteConstant.ICONS.Dashboard_Icon
            }
          />
        ),
      },
      {
        id: 3,
        name: "Planner",
        link: "/planner",
        logo: (
          <img
            alt="Planner"
            className="h-8 w-8 p-1.5"
            src={
              location.pathname === "/planner"
                ? siteConstant.ICONS.Planner_Active
                : siteConstant.ICONS.Planner
            }
          />
        ),
      },
      {
        id: 4,
        name: "Messages",
        link: "/chat",
        logo: (
          <img
            alt="Messages"
            className="h-8 w-8 p-1.5"
            src={
              location.pathname === "/chat"
                ? siteConstant.ICONS.Messages_Active
                : siteConstant.ICONS.Messages
            }
          />
        ),
        // badge: notificationCount,
      },
      {
        id: 5,
        name: "Analytics",
        link: "/analytics",
        logo: (
          <img
            alt="Analytics"
            className="h-8 w-8 p-1.5"
            src={
              location.pathname === "/analytics"
                ? siteConstant.ICONS.Analytics_Active
                : siteConstant.ICONS.Analytics
            }
          />
        ),
      },
      // {
      //   id: 7,
      //   name: "Upload Post",
      //   action: "uploadPost",
      //   logo: (
      //     <img
      //       alt="Upload Post"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.Add_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 8,
      //   name: "Scheduled Post",
      //   link: "/scheduled-post",
      //   logo: (
      //     <img
      //       alt="Scheduled Post"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.Time_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 5,
      //   name: "User Management",
      //   link: "/UserManagement",
      //   logo: (
      //     <img
      //       alt="scheduledicon"
      //       className="h-10 w-10 p-2 bg-white rounded-[14px]  "
      //       src={siteConstant.ICONS.Time_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 6,
      //   name: "Chat",
      //   link: "/chat",
      //   logo: (
      //     <img
      //       alt="scheduledicon"
      //       className="h-10 w-10 p-2 bg-white rounded-[14px]  "
      //       src={siteConstant.ICONS.Time_Icon}
      //     />
      //   ),
      // },
      {
        id: 7,
        name: "Live Stream",
        link: "/live",
        logo: (
          <img
            alt="scheduledicon"
            className="h-8 w-8 p-1.5"
            src={siteConstant.ICONS.Live_Icon}
          />
        ),
      },

      // {
      //   id: 8,
      //   name: "Brands",
      //   action: "brands",
      //   link: "/brands",
      //   logo: (
      //     <img
      //       alt="brands"
      //       className="h-10 w-10 p-2 bg-white rounded-[14px]  "
      //       src={siteConstant.ICONS.Add_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 9,
      //   name: "User Management",
      //   link: "/UserManagement",
      //   logo: (
      //     <img
      //       alt="User Management"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.User_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 10,
      //   name: "Chat",
      //   link: "/chat",
      //   logo: (
      //     <img
      //       alt="Chat"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.Time_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 11,
      //   name: "Live Stream",
      //   link: "/live",
      //   logo: (
      //     <img
      //       alt="Live Stream"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.Time_Icon}
      //     />
      //   ),
      // },
      // {
      //   id: 12,
      //   name: "Brands",
      //   link: "/brands",
      //   logo: (
      //     <img
      //       alt="Brands"
      //       className="h-5 w-5"
      //       src={siteConstant.ICONS.Brands}
      //     />
      //   ),
      // },
      // {
      //   id: 13,
      //   name: "Feedback",
      //   action: "feedback",
      //   logo: (
      //     <img
      //       alt="Feedback"
      //       className="h-8 w-8 p-1.5"
      //       src={siteConstant.ICONS.Feed_Icon}
      //     />
      //   ),
      // },
    ];

    if (currentTab === localesData?.ADMIN_TAB) {
      setNavbarProps(adminNavItems);
    } else {
      setNavbarProps(userNavItems);
    }
  }, [userData, currentTab, firstName, location.pathname, notificationCount]);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDropdownOpen = (event) => {
    setDropdownAnchorEl(event.currentTarget);
  };

  const handleDropdownClose = () => {
    setDropdownAnchorEl(null);
  };

  // Function to join Flowkar socket
  const joinFlowkarSocket = () => {
    if (!socket || !socket.connected) {
      console.error("Socket not connected");
      return;
    }

    const socketData = {
      Authorization: `Bearer ${token}`,
    };

    console.log("Joining Flowkar socket...", socketData);
    socket.emit("join_socket", socketData);

    // Listen for join response
    const handleJoinResponse = (response) => {
      console.log("Join socket response:", response);
      if (response.message === "JWT Token Required") {
        console.error("JWT Token Required for Flowkar socket");
        // Could show a toast notification here instead of alert
      } else if (
        response.success ||
        response.message === "Successfully joined"
      ) {
        console.log("Successfully joined Flowkar socket");
      }
      // Remove the listener after handling
      socket.off("join_socket", handleJoinResponse);
    };

    // Set up response listener
    socket.on("join_socket", handleJoinResponse);

    // Set a timeout to remove listener if no response
    setTimeout(() => {
      socket.off("join_socket", handleJoinResponse);
    }, 5000);
  };

  const handleUploadPostOpen = () => {
    setIsUploadDialogOpen(true);
  };

  const handleFeedbackOpen = () => {
    setIsFeedOpen(true);
  };

  const handleUploadPostClose = () => {
    setIsUploadDialogOpen(false);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const logout = () => {
    clearStorage();
    resetBrand();
    document.cookie.split(";").forEach((cookie) => {
      const name = cookie.trim().split("=")[0];
      document.cookie = `${name}=;expires=${new Date(0).toUTCString()};path=/`;
    });
    localStorage.clear();
    sessionStorage.clear();
    navigate("/sign-in", { replace: true });
  };

  const switchTab = () => {
    if (currentTab === localesData?.ADMIN_TAB) {
      saveToStorage(
        siteConstant?.INDENTIFIERS.currentTab,
        localesData?.USER_TAB
      );
      setCurrentTab(localesData?.USER_TAB);
      navigate("/dashboard");
      setIsChecked(false);
      handleMenuClose();
    } else {
      saveToStorage(
        siteConstant?.INDENTIFIERS.currentTab,
        localesData?.ADMIN_TAB
      );
      setCurrentTab(localesData?.ADMIN_TAB);
      navigate("/admin/user-management");
      setIsChecked(true);
      handleMenuClose();
    }
  };

  // Mobile drawer content with updated styling for active items
  const drawer = (
    <Box
      onClick={handleDrawerToggle}
      sx={{ textAlign: "center", pt: 2, pb: 2 }}
    >
      <Box className="flex justify-center items-center mb-4">
        <img
          src={siteConstant.SOCIAL_ICONS.FLOWKAR_TEXT}
          alt="Flowkar Logo"
          className="h-10 w-20"
        />
      </Box>
      <Divider />
      <List>
        {navbarProps.map((item) => {
          const isActive = location.pathname === item.link;
          return (
            <ListItem
              key={item.id}
              button
              onClick={() => {
                if (item.action === "uploadPost") {
                  handleUploadPostOpen();
                } else if (item.action === "feedback") {
                  handleFeedbackOpen();
                } else {
                  // Join Flowkar socket when Messages button is clicked
                  if (item.link === "/chat") {
                    joinFlowkarSocket();
                  }
                  navigate(item.link);
                }
                setMobileOpen(false);
              }}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                padding: "8px 16px",
                "&.Mui-selected": {
                  backgroundColor: "#F5F5F5",
                },
                color: isActive ? "#674941" : "#9E9E9E",
              }}
              selected={isActive}
            >
              <ListItemIcon
                sx={{
                  minWidth: "40px",
                  "& img": {
                    filter: isActive ? "none" : "grayscale(100%)",
                    opacity: isActive ? 1 : 0.7,
                  },
                }}
              >
                {item.badge ? (
                  <Badge badgeContent={item.badge} color="error">
                    {item.logo}
                  </Badge>
                ) : (
                  item.logo
                )}
              </ListItemIcon>
              <ListItemText
                primary={item.name}
                sx={{
                  "& .MuiListItemText-primary": {
                    color: isActive ? "#674941" : "#9E9E9E",
                    fontWeight: isActive ? 600 : 400,
                  },
                }}
              />
              {isActive && (
                <Box
                  sx={{
                    width: "4px",
                    height: "4px",
                    borderRadius: "50%",
                    backgroundColor: "#674941",
                    ml: 1,
                  }}
                />
              )}
            </ListItem>
          );
        })}
      </List>
    </Box>
  );

  // Add state for notification popup
  // Remove Dialog state, use anchorEl for Popover
  const [notifyAnchorEl, setNotifyAnchorEl] = useState(null);

  function HandleNotify(event) {
    setNotifyAnchorEl(event.currentTarget);
  }

  function handleNotifyClose() {
    setNotifyAnchorEl(null);
  }

  // Remove Menu state, use anchorEl for Popover
  const [profileAnchorEl, setProfileAnchorEl] = useState(null);

  function handleProfileOpen(event) {
    setProfileAnchorEl(event.currentTarget);
  }

  function handleProfileClose() {
    setProfileAnchorEl(null);
  }

  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
      >
        <CssBaseline />
        <AppBar
          position="fixed"
          sx={{
            boxShadow: "none",
            backgroundColor: isScrolled
              ? "rgba(255, 255, 255, 0.7)"
              : "transparent",
            transition: "background-color 0.3s ease, backdrop-filter 0.3s ease",
            zIndex: (theme) => theme.zIndex.drawer + 1,
          }}
          className={`header ${isScrolled ? "header-blur" : ""}`}
        >
          <Toolbar className="flex justify-between items-center py-1 font-Ubuntu bg-primarBG h-[80px] rounded-br-[12px] rounded-bl-[12px] shadow-xl">
            {/* Left side - Logo and hamburger menu for mobile */}
            <Box className="flex items-center">
              {/* Show hamburger menu only for xs and sm screens */}
              {isMobile && (
                <IconButton
                  color="black"
                  aria-label="open drawer"
                  edge="start"
                  onClick={handleDrawerToggle}
                  sx={{ mr: 2 }}
                >
                  <MenuIcon />
                </IconButton>
              )}

              <div
                className="p-1 px-2 m-1 cursor-pointer"
                onClick={() => navigate("/dashboard")}
              >
                <img
                  src={siteConstant.SOCIAL_ICONS.FLOWKAR_TEXT}
                  alt="Flowkar Logo"
                  className="h-8 w-16 sm:h-10 sm:w-20 md:h-12 md:w-32 lg:h-[51px] lg:w-44"
                />
              </div>
            </Box>

            {/* Center - Navigation Icons (visible on non-mobile screens) */}
            {!isMobile && (
              <Box className="flex-grow flex justify-center items-center space-x-1 sm:space-x-2 md:space-x-3 lg:space-x-5">
                {navbarProps.slice(1, 13).map((item) => {
                  const isActive = location.pathname === item.link;

                  const handleItemClick = () => {
                    if (item.action === "uploadPost") {
                      handleUploadPostOpen();
                    } else if (item.action === "feedback") {
                      handleFeedbackOpen();
                    } else {
                      // Join Flowkar socket when Messages button is clicked
                      if (item.link === "/chat") {
                        joinFlowkarSocket();
                      }
                      navigate(item.link);
                    }
                  };

                  return (
                    <div
                      key={item.id}
                      className="flex flex-col items-center cursor-pointer "
                      onClick={handleItemClick}
                    >
                      <IconButton
                        sx={{
                          p: { sm: 0.3, md: 0.5 },
                          backgroundColor: isActive ? "tran" : "transparent",
                          "& img": {
                            filter: isActive ? "none" : "grayscale(100%)",
                            opacity: isActive ? 1 : 0.7,
                          },
                        }}
                      >
                        {item.badge ? (
                          <Badge badgeContent={item.badge} color="error">
                            {item.logo}
                          </Badge>
                        ) : (
                          item.logo
                        )}
                      </IconButton>
                      {/* Text shown below icon - conditionally show based on screen size */}
                      <Typography
                        variant="caption"
                        sx={{
                          display: { sm: "none", md: "block" },
                          color: isActive ? "#674941" : "#9E9E9E",
                          fontWeight: isActive ? 600 : 400,
                          fontSize: { md: "9px", lg: "10px" },
                          mt: 0.5,
                        }}
                      >
                        {item.name}
                      </Typography>
                      {/* Show dot indicator for active page */}
                      {isActive && (
                        <div className="h-1 w-1 bg-[#674941] rounded-full mt-1"></div>
                      )}
                    </div>
                  );
                })}
              </Box>
            )}

            {/* Right side - Profile & Menu with responsive spacing */}
            <Box className="flex items-center">
              <Box
                className="mr-2 sm:mr-3 md:mr-4"
                sx={{
                  width: { xs: "140px", sm: "160px", md: "180px", lg: "200px" },
                  flexShrink: 0,
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <IconButton
                  onClick={handleDropdownOpen}
                  sx={{
                    width: "100%",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderRadius: "10px",
                    padding: "10px 14px",
                    backgroundColor: "#f9f9f9",
                    transition: "all 0.2s ease-in-out",
                    "&:hover": {
                      backgroundColor: "#f1f1f1",
                      boxShadow: "0 1px 5px rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      flex: 1,
                      minWidth: 0,
                    }}
                  >
                    <div className="flex justify-center items-center gap-2">
                      <Box
                        sx={{
                          width: {
                            xs: "20px",
                            sm: "24px",
                            md: "28px",
                            lg: "32px",
                          },
                          height: {
                            xs: "20px",
                            sm: "24px",
                            md: "28px",
                            lg: "32px",
                          },
                          flexShrink: 0,
                        }}
                      >
                        <img
                          src={
                            currentUser?.profile_image ||
                            siteConstant.SOCIAL_ICONS.FLOWKARLOGO
                          }
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src =
                              siteConstant.SOCIAL_ICONS.FLOWKARLOGO;
                          }}
                          alt={currentUser?.name}
                          style={{
                            width: "100%",
                            height: "100%",
                            borderRadius: "50%",
                            objectFit: "cover",
                          }}
                        />
                      </Box>

                      <Typography
                        sx={{
                          color: "#333333",
                          fontWeight: 500,
                          fontFamily: "Ubuntu",
                          fontSize: {
                            xs: "10px !important",
                            sm: "12px !important",
                            md: "14px !important",
                            lg: "14px !important",
                          },
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          flex: 1,
                          minWidth: 0,
                        }}
                      >
                        {currentUser
                          ? currentUser.name.length > 10
                            ? currentUser.name.slice(0, 10) + "..."
                            : currentUser.name
                          : "Select User"}
                      </Typography>
                    </div>
                  </Box>

                  <KeyboardArrowDownIcon
                    sx={{
                      fontSize: "12px",
                      color: "#563D39",
                      flexShrink: 0,
                      ml: 1,
                    }}
                  />
                </IconButton>

                <Menu
                  anchorEl={dropdownAnchorEl}
                  open={Boolean(dropdownAnchorEl)}
                  onClose={handleDropdownClose}
                  sx={{
                    "& .MuiPaper-root": {
                      minWidth: "220px",
                      maxHeight: "300px",
                      overflowY: "auto",
                      boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)",
                      borderRadius: "10px",
                      padding: "4px 0",
                    },
                  }}
                >
                  {loadingUsers ? (
                    <MenuItem disabled sx={{ py: 1 }}>
                      <CircularProgress size={20} sx={{ mr: 2 }} />
                      Loading users...
                    </MenuItem>
                  ) : (
                    <>
                      {Array.isArray(availableUsers) &&
                        availableUsers.map((user) => {
                          const isSelected =
                            currentUser?.id === user.id ||
                            currentUser?.user_id === user.id ||
                            currentUser?.id === user.user_id ||
                            currentUser?.user_id === user.user_id;

                          // Debug logging for user selection (can be removed in production)
                          // if (user.id === 1) {
                          //   console.log("Debug user selection:", {
                          //     userId: user.id,
                          //     userUserId: user.user_id,
                          //     currentUserId: currentUser?.id,
                          //     currentUserUserId: currentUser?.user_id,
                          //     isSelected,
                          //   });
                          // }
                          return (
                            <MenuItem
                              key={user.id}
                              onClick={() => {
                                onUserChange(user);
                              }}
                              sx={{
                                alignItems: "flex-start",
                                display: "flex",
                                gap: 2,
                                px: 2,
                                py: 2,
                                borderBottom: "1px solid #eee",
                                backgroundColor: isSelected
                                  ? "#e3f2fd"
                                  : "transparent",
                                "&:hover": {
                                  backgroundColor: isSelected
                                    ? "#bbdefb"
                                    : "#f5f5f5",
                                },
                              }}
                            >
                              <Box
                                sx={{
                                  width: 48,
                                  height: 48,
                                  borderRadius: "50%",
                                  position: "relative",
                                  flexShrink: 0,
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                {imageLoading && (
                                  <CircularProgress
                                    size={24}
                                    sx={{
                                      position: "absolute",
                                      zIndex: 1,
                                    }}
                                  />
                                )}

                                {!imageError && user.profile_image ? (
                                  <img
                                    src={user.profile_image}
                                    alt={user.name}
                                    onLoad={() => {
                                      setImageLoading(false);
                                      setImageError(false);
                                    }}
                                    onError={() => {
                                      setImageLoading(false);
                                      setImageError(true);
                                    }}
                                    style={{
                                      width: 48,
                                      height: 48,
                                      borderRadius: "100%",
                                      objectFit: "cover",
                                      opacity: imageLoading ? 0 : 1,
                                      transition: "opacity 0.3s ease-in-out",
                                    }}
                                  />
                                ) : (
                                  <Box
                                    sx={{
                                      width: 48,
                                      height: 48,
                                      borderRadius: "50%",
                                      backgroundColor: "#f0f0f0",
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "center",
                                    }}
                                  >
                                    <Typography
                                      sx={{ fontSize: "12px", color: "#999" }}
                                    >
                                      {user.name.charAt(0).toUpperCase()}
                                    </Typography>
                                  </Box>
                                )}
                              </Box>

                              <Box sx={{ minWidth: 0, flex: 1 }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      fontFamily: "Ubuntu",
                                      fontSize: "18px",
                                      fontWeight: 500,
                                      color: "#000000",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                      flex: 1,
                                    }}
                                  >
                                    {user.name}
                                  </Typography>
                                  {userData &&
                                    (user.id === userData.user_id ||
                                      user.user_id === userData.user_id) && (
                                      <Box
                                        sx={{
                                          backgroundColor: "#4caf50",
                                          color: "white",
                                          fontSize: "10px",
                                          fontWeight: "bold",
                                          px: 1,
                                          py: 0.5,
                                          borderRadius: "8px",
                                          flexShrink: 0,
                                        }}
                                      >
                                        YOU
                                      </Box>
                                    )}
                                </Box>
                                <Typography
                                  sx={{
                                    fontFamily: "Ubuntu",
                                    fontSize: "16px",
                                    color: "#A9ABAD",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                  }}
                                >
                                  @
                                  {user.username ||
                                    user.email ||
                                    `ID: ${user.user_id}`}
                                </Typography>
                                <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                                  {Object.entries(user)
                                    .filter(([key, value]) => {
                                      // Filter for platform keys that are true
                                      const platformKeys = [
                                        "Facebook",
                                        "Instagram",
                                        "x",
                                        "YouTube",
                                        "LinkedIn",
                                        "Pinterest",
                                        "tiktok",
                                        "threads",
                                        "Dailymotion",
                                        "Twitter",
                                        "tumblr",
                                        "Vimeo",
                                        "reddit",
                                        "telegram",
                                        "mastodon",
                                      ];
                                      return (
                                        platformKeys.includes(key) &&
                                        value === true
                                      );
                                    })
                                    .map(([platform]) => {
                                      // Transform platform name to match icon key
                                      let iconKey;
                                      if (platform === "x") {
                                        iconKey = "X_ICON";
                                      } else {
                                        iconKey = `${platform.toUpperCase()}_ICON`;
                                      }

                                      const iconSrc =
                                        siteConstant.SOCIAL_ICONS[iconKey];
                                      if (!iconSrc) return null; // Handle missing icons

                                      return (
                                        <img
                                          key={platform}
                                          src={iconSrc}
                                          alt={platform}
                                          style={{
                                            width: 26,
                                            height: 26,
                                            borderRadius: "100%",
                                            marginLeft: "-12px",
                                          }}
                                        />
                                      );
                                    })}
                                </Box>
                              </Box>
                            </MenuItem>
                          );
                        })}

                      {/* Add Other Account MenuItem */}
                      <MenuItem
                        onClick={() => {
                          navigate("/signInn");
                        }}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 2,
                          px: 2,
                          py: 2,
                          borderTop: "1px solid #eee",
                          "&:hover": {
                            backgroundColor: "#f5f5f5",
                          },
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: "50%",
                            backgroundColor: "#f0f0f0",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexShrink: 0,
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: "24px",
                              color: "#674941",
                              fontWeight: "300",
                            }}
                          >
                            +
                          </Typography>
                        </Box>
                        <Typography
                          sx={{
                            fontFamily: "Ubuntu",
                            fontSize: "16px",
                            fontWeight: 500,
                            color: "#674941",
                          }}
                        >
                          Add Other Account
                        </Typography>
                      </MenuItem>
                    </>
                  )}
                </Menu>
              </Box>

              <IconButton
                onClick={HandleNotify}
                className="flex text-sm rounded-full focus:ring-gray-300"
                sx={{ flexShrink: 0 }}
              >
                <img
                  src={Notification}
                  alt="DropDown"
                  className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 rounded-2xl p-1"
                />
              </IconButton>
              <Popover
                className="ml-[-10px] mt-[13px]"
                id={
                  Boolean(notifyAnchorEl) ? "notification-popover" : undefined
                }
                open={Boolean(notifyAnchorEl)}
                anchorEl={notifyAnchorEl}
                onClose={handleNotifyClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                PaperProps={{
                  sx: {
                    mt: 1,
                    borderRadius: 2,
                    minWidth: 400,
                    boxShadow: "1 8px 32px rgba(80, 80, 80, 0.10)",
                    p: 2,
                    background: "#fff",
                    transition: "all 0.2s cubic-bezier(0.4,0,0.2,1)",
                  },
                }}
                PopperProps={{
                  modifiers: [
                    {
                      name: "offset",
                      options: {
                        offset: [-20, 10],
                      },
                    },
                  ],
                }}
              >
                <Notifypage handleNotifyClose={handleNotifyClose} />
              </Popover>

              <IconButton
                onClick={handleProfileOpen}
                className="flex text-sm bg-gray-500 rounded-full focus:ring-gray-300"
                sx={{ flexShrink: 0 }} // Prevent this button from shrinking
              >
                <img
                  src={dropDownNav}
                  alt="DropDown"
                  className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 rounded-2xl p-1"
                />
              </IconButton>
              <Popover
                className="ml-[-10px] mt-[11px]"
                id={Boolean(profileAnchorEl) ? "profile-popover" : undefined}
                open={Boolean(profileAnchorEl)}
                anchorEl={profileAnchorEl}
                onClose={handleProfileClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                PaperProps={{
                  sx: {
                    mt: 1,
                    borderRadius: 2,
                    minWidth: 300,
                    boxShadow: "1 8px 32px rgba(80, 80, 80, 0.10)",
                    p: 2,
                    background: "#fff",
                    transition: "all 0.2s cubic-bezier(0.4,0,0.2,1)",
                  },
                }}
                PopperProps={{
                  modifiers: [
                    {
                      name: "offset",
                      options: {
                        offset: [-20, 10],
                      },
                    },
                  ],
                }}
              >
                <div>
                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                      },
                    }}
                    onClick={() => {
                      navigate("/profile");
                      handleProfileClose();
                    }}
                  >
                    <IconButton edge="start" color="inherit" aria-label="user">
                      <img
                        src={
                          currentUser?.profile_image ||
                          siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                        }
                        alt="User Photo"
                        className="h-7 w-7 bg-cover object-cover  rounded-full"
                        onClick={() => {
                          handleProfileClose();
                        }}
                      />
                    </IconButton>
                    <p className="text-sm font-normal font-Ubuntu text-Red">
                      {currentUser?.name}
                    </p>
                  </MenuItem>
                  <Divider />
                  {/* {userData?.is_admin && (
                    <MenuItem
                      sx={{
                        '&:hover': {
                          backgroundColor: '#EFEBE9',
                          color: '#674941',
                        },
                      }}
                      onClick={switchTab}
                    >
                      <IconButton
                        edge="start"
                        color="inherit"
                        aria-label="switch"
                      >
                        <img
                          className="h-4 w-4 ms-1"
                          src={siteConstant.SOCIAL_ICONS.USER}
                          alt="Switch"
                        />
                      </IconButton>
                      <p className="text-sm font-semibold font-Ubuntu text-Red ms-[2px]">
                        {localesData?.USER_WEB?.SWITCH} (
                        {currentTab === localesData?.ADMIN_TAB
                          ? localesData?.USER_TAB
                          : localesData?.ADMIN_TAB}
                        )
                      </p>
                      <div className="ps-4">
                        <Android12Switch
                          sx={{
                            width: 32,
                            height: 18,
                            '& .MuiSwitch-thumb': {
                              width: 14,
                              height: 14,
                            },
                            '& .MuiSwitch-track': {
                              borderRadius: 13,
                            },
                          }}
                          checked={isChecked}
                        />
                      </div>
                    </MenuItem>
                  )} */}

                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                        color: "#674941",
                      },
                    }}
                    onClick={() => {
                      navigate("/plateforms");
                      handleProfileClose();
                    }}
                  >
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="delete"
                    >
                      <img
                        className="h-4 w-4 ms-1 text-Red"
                        src={siteConstant.SOCIAL_ICONS.BRAND_MANAGEMENT}
                        alt="Delete Account"
                      />
                    </IconButton>
                    <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
                      Plateforms
                    </p>
                  </MenuItem>
                  <Divider />

                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                        color: "#674941",
                      },
                    }}
                    onClick={() => {
                      navigate("/UserManagement");
                      handleProfileClose();
                    }}
                  >
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="delete"
                    >
                      <img
                        className="h-4 w-4 ms-1 text-Red"
                        src={siteConstant.SOCIAL_ICONS.USER_MANAGEMENT}
                        alt="Delete Account"
                      />
                    </IconButton>
                    <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
                      {localesData?.USER_WEB?.USER_MANAGEMENT?.USER_TITLE}
                    </p>
                  </MenuItem>
                  <Divider />

                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                        color: "#674941",
                      },
                    }}
                    onClick={() => {
                      navigate("/feedback");
                      handleProfileClose();
                    }}
                  >
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="delete"
                    >
                      <img
                        className="h-4 w-4 ms-1 text-Red"
                        src={siteConstant.SOCIAL_ICONS.FEEDBACK}
                        alt="Delete Account"
                      />
                    </IconButton>
                    <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
                      {localesData?.USER_WEB?.FEEDBACK}
                    </p>
                  </MenuItem>
                  <Divider />

                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                        color: "#674941",
                      },
                    }}
                    onClick={() => {
                      setOpenDelete(!openDelete);
                      handleProfileClose();
                    }}
                  >
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="delete"
                    >
                      <img
                        className="h-4 w-4 ms-1 text-Red"
                        src={siteConstant.SOCIAL_ICONS.DELETE}
                        alt="Delete Account"
                      />
                    </IconButton>
                    <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
                      {localesData?.USER_WEB?.DELETE_ACCOUNT}
                    </p>
                  </MenuItem>
                  <Divider />
                  <MenuItem
                    sx={{
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                      },
                    }}
                    onClick={logout}
                  >
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="sign-out"
                    >
                      <img
                        className="h-4 w-4 ms-1"
                        src={siteConstant.SOCIAL_ICONS.SIGN_OUT}
                        alt="Sign Out"
                      />
                    </IconButton>
                    <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
                      {localesData?.USER_WEB?.SIGN_OUT}
                    </p>
                  </MenuItem>
                </div>
              </Popover>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Main content with proper spacing and transitions */}
        <Box
          component="main"
          className="bg-[#F9F9F9]"
          sx={{
            flexGrow: 1,
            p: { xs: 1, sm: 1.5, md: 2 },
            width: "100%",
            mt: { xs: "80px", sm: "82px", md: "85px" },
            mb: isMobile ? "64px" : 0,
            position: "relative",
            overflow: "hidden",
          }}
        >
          <div
            className="overflow-auto h-screen font-Ubuntu"
            id="scrollDiv"
            style={{
              position: "relative",
              zIndex: 1,
              transition: "opacity 0.2s ease-in-out",
            }}
          >
            <Suspense
              fallback={
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                ></div>
              }
            >
              <Outlet />
            </Suspense>
            {isVisible && <Scrolltop isVisible={isVisible} />}
          </div>
        </Box>

        {/* Mobile navigation drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: 240,
              transition: "transform 0.3s ease-in-out",
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Modals and Dialogs */}
        {isUploadDialogOpen && (
          <Suspense fallback={<div>Loading...</div>}>
            <UploadPost
              open={isUploadDialogOpen}
              onClose={handleUploadPostClose}
            />
          </Suspense>
        )}

        {isFeedOpen && (
          <Suspense fallback={<div>Loading...</div>}>
            <Feedpage setIsFeedOpen={setIsFeedOpen} isFeedOpen={isFeedOpen} />
          </Suspense>
        )}

        {/* Notification Popup Dialog */}
        {/* Remove the Dialog-based notification popup */}

        <DeleteDialogueModel
          open={openDelete}
          handleDialogClose={() => setOpenDelete(!openDelete)}
        />
      </Box>
    </ThemeProvider>
  );
};

export default Navbar;
