import axios from "axios";
import { clearStorage, fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

let isRedirecting = false;
const apiInstance = axios.create({
  baseURL: "https://api.flowkar.com/api",
  withCredentials: true,
});

apiInstance.interceptors.request.use((config) => {
  // Get user data from localStorage
  const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
  const switchUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );

  // Use switched user data if available, otherwise use original user data
  const activeUser = switchUserData || userData;

  const token = userData?.token; // Always use original user's token

  // Get user ID and brand ID from active user (switched user takes priority)
  let userId = activeUser?.user_id;
  let brandId = activeUser?.brand_id;

  // Fallback to localStorage if not found in user data
  if (!userId) {
    userId = localStorage.getItem("UserId");
  }
  if (!brandId) {
    brandId = localStorage.getItem("BrandId");
  }

  // Debug logging to check values
  console.log("API Request Headers Debug:", {
    activeUser: activeUser?.name,
    userId,
    brandId,
    switchUserData: switchUserData?.name,
    originalUser: userData?.name,
  });

  const clonedConfig = config;
  if (token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${token}`,
      user: userId,
      brand: brandId,
      ...clonedConfig.headers,
      "Content-Type":
        clonedConfig.headers["Content-Type"] || "multipart/form-data",
    };
  } else {
    clonedConfig.headers = {
      "Content-Type": "multipart/form-data",
      ...clonedConfig.headers,
    };
  }
  return clonedConfig;
});

apiInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;
      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
        clearStorage();
      } else if (status === 400) {
        console.error("Error:", error.response.data.message);
      }
    }
    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default apiInstance;
