import React, { createContext, useContext, useState, useEffect } from "react";
import { fetchFromStorage, saveToStorage } from "./storage";
import siteConstant from "../constant/siteConstant";

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [userData, setUserData] = useState(null);
  const [switchUserData, setSwitchUserData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get the currently active user (switched user takes priority)
  const activeUser = switchUserData || userData;

  // Initialize user data from localStorage
  useEffect(() => {
    const initializeUserData = () => {
      try {
        const storedUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        const storedSwitchUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA);
        
        setUserData(storedUserData);
        setSwitchUserData(storedSwitchUserData);
        
        // Update localStorage with active user's IDs
        if (storedSwitchUserData) {
          localStorage.setItem("UserId", storedSwitchUserData.user_id?.toString() || "");
          localStorage.setItem("BrandId", storedSwitchUserData.brand_id?.toString() || "");
        } else if (storedUserData) {
          localStorage.setItem("UserId", storedUserData.user_id?.toString() || "");
          localStorage.setItem("BrandId", storedUserData.brand_id?.toString() || "");
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error("Error initializing user data:", error);
        setIsLoading(false);
      }
    };

    initializeUserData();
  }, []);

  // Update localStorage whenever active user changes
  useEffect(() => {
    if (activeUser) {
      localStorage.setItem("UserId", activeUser.user_id?.toString() || "");
      localStorage.setItem("BrandId", activeUser.brand_id?.toString() || "");
    }
  }, [activeUser]);

  // Function to update user data (for login/logout)
  const updateUserData = (newUserData) => {
    setUserData(newUserData);
    saveToStorage(siteConstant?.INDENTIFIERS?.USERDATA, newUserData);
    
    if (newUserData) {
      localStorage.setItem("UserId", newUserData.user_id?.toString() || "");
      localStorage.setItem("BrandId", newUserData.brand_id?.toString() || "");
    } else {
      localStorage.removeItem("UserId");
      localStorage.removeItem("BrandId");
    }
  };

  // Function to switch user
  const switchUser = (newSwitchUserData) => {
    setSwitchUserData(newSwitchUserData);
    saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, newSwitchUserData);
    
    if (newSwitchUserData) {
      localStorage.setItem("UserId", newSwitchUserData.user_id?.toString() || "");
      localStorage.setItem("BrandId", newSwitchUserData.brand_id?.toString() || "");
    } else if (userData) {
      // Switching back to original user
      localStorage.setItem("UserId", userData.user_id?.toString() || "");
      localStorage.setItem("BrandId", userData.brand_id?.toString() || "");
    }
  };

  // Function to clear switch user (go back to original user)
  const clearSwitchUser = () => {
    setSwitchUserData(null);
    saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, null);
    
    if (userData) {
      localStorage.setItem("UserId", userData.user_id?.toString() || "");
      localStorage.setItem("BrandId", userData.brand_id?.toString() || "");
    }
  };

  // Function to get current user ID for API calls
  const getCurrentUserId = () => {
    return activeUser?.user_id || null;
  };

  // Function to get current brand ID for API calls
  const getCurrentBrandId = () => {
    return activeUser?.brand_id || localStorage.getItem("BrandId") || null;
  };

  // Function to get current user token
  const getCurrentToken = () => {
    return userData?.token || null; // Always use original user's token
  };

  const value = {
    userData,
    switchUserData,
    activeUser,
    isLoading,
    updateUserData,
    switchUser,
    clearSwitchUser,
    getCurrentUserId,
    getCurrentBrandId,
    getCurrentToken,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export default UserContext;
